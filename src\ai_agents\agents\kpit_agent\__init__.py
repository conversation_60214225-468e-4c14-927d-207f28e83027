"""KPIT Agent implementation using <PERSON><PERSON><PERSON><PERSON> ReAct pattern.

A ReAct-style autonomous agent that reasons and acts across three systems:
- GitLab repositories (read file SHAs and contents, time-based file versions)
- SQL Server databases (list tables, get schema, validate/run queries)
- Local filesystem (list/search logs, list/inspect PCAPs)

Uses LangChain's ReAct loop with the Mistral LLM to plan tool calls and synthesize answers.
"""

import os
from typing import Any, Dict, List
from langchain_core.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_functions_agent
from ..agent_base import AgentBase
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder


class KPITAgent(AgentBase):
    """
    A KPIT agent implemented using Lang<PERSON>hain's ReAct agent pattern.

    Uses GitLab, SQL Server, and filesystem tools to automate workflows.
    """

    default_config_path = os.path.join(os.path.dirname(__file__), "config.yaml")

    def __init__(self, config: Dict[str, Any]):
        """Initialize the KPIT agent."""
        self.config = config
        self.tools = self.build_tools()
        self.model = self.build_model()
        self.agent = self.build_agent()

    def build_tools(self) -> List[BaseTool]:
        """Build the agent's toolset."""
        # Import tools locally to avoid circular imports
        from .tools.gitlab_tools import (
            GitLabFileSHATool,
            GitLabFileContentTool,
            GitLabFileSHAFromTimestampTool,
        )
        from .tools.sql_tools import (
            SQLDatabaseListTablesTool,
            SQLDatabaseSchemaTool,
            SQLDatabaseQueryCheckerTool,
            SQLDatabaseQueryTool,
        )
        from .tools.filesystem_tools import (
            ListLogFilesTool,
            SearchLogsTool,
            GetLogContentTool,
            ListPCAPFilesTool,
            GetPCAPInfoTool,
        )
        
        # GitLab configuration
        gitlab_config = {
            'gitlab_url': 'https://gitlab.com',
            'project_id': self.config.get('gitlab', {}).get('project_id', '71217006'),
            'token': self.config.get('gitlab', {}).get('token', '*********************************************************')
        }
        
        # SQL Database configuration
        sql_config = self.config.get('sql', {})
        db_instance = None
        if sql_config.get('enabled', False):
            from langchain_community.utilities import SQLDatabase
            connection_string = f"mssql+pyodbc://@{sql_config.get('server', 'localhost')}/{sql_config.get('database', 'STAGE')}?driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes"
            try:
                db_instance = SQLDatabase.from_uri(connection_string)
            except Exception as e:
                print(f"Warning: Could not connect to SQL database: {e}")
        
        sql_db_config = {
            'db': db_instance,
            'llm': self.model
        }
        
        # Filesystem configuration
        filesystem_config = {
            'log_folder': self.config.get('filesystem', {}).get('log_folder', './logs'),
            'pcap_folder': self.config.get('filesystem', {}).get('pcap_folder', './pcaps')
        }

        tools = []
        
        # GitLab tools
        try:
            tools.extend([
                GitLabFileSHATool(gitlab_config),
                GitLabFileContentTool(gitlab_config),
                GitLabFileSHAFromTimestampTool(gitlab_config),
            ])
        except Exception as e:
            print(f"Warning: Could not initialize GitLab tools: {e}")
        
        # SQL tools
        if db_instance:
            try:
                tools.extend([
                    SQLDatabaseListTablesTool(sql_db_config),
                    SQLDatabaseSchemaTool(sql_db_config),
                    SQLDatabaseQueryCheckerTool(sql_db_config),
                    SQLDatabaseQueryTool(sql_db_config),
                ])
            except Exception as e:
                print(f"Warning: Could not initialize SQL tools: {e}")
        
        # Filesystem tools
        try:
            tools.extend([
                ListLogFilesTool(filesystem_config),
                SearchLogsTool(filesystem_config),
                GetLogContentTool(filesystem_config),
                ListPCAPFilesTool(filesystem_config),
                GetPCAPInfoTool(filesystem_config),
            ])
        except Exception as e:
            print(f"Warning: Could not initialize filesystem tools: {e}")

        return tools

    def build_agent(self) -> AgentExecutor:
        """
        Build the LangChain agent executor.

        Returns:
            AgentExecutor: The configured LangChain agent executor
        """
        # Create the agent prompt
        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are a KPIT automation agent that can work with GitLab repositories, SQL Server databases, and local filesystems. "
                    "ALWAYS use your tools to gather information before responding. "
                    "For any query, analyze what systems you need to interact with and use the appropriate tools.\n\n"
                    "Available systems:\n"
                    "1. GitLab - Use file SHA tools to get file versions, then content tools to read files\n"
                    "2. SQL Server - List tables first, get schema if needed, validate queries before running\n"
                    "3. Filesystem - List files first, then search or get content as needed\n\n"
                    "Process:\n"
                    "1. ALWAYS start by using appropriate tools to gather information\n"
                    "2. Review and analyze the results\n"
                    "3. Use additional tools if needed for more context\n"
                    "4. Synthesize all findings into a comprehensive response",
                ),
                MessagesPlaceholder(variable_name="chat_history", optional=True),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ]
        )

        # Create the agent using OpenAI functions format
        agent = create_openai_functions_agent(
            llm=self.model, tools=self.tools, prompt=prompt
        )

        # Create the executor
        return AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=self.tools,
            verbose=self.config.get("verbose", True),
            handle_parsing_errors=True,
            max_iterations=self.config.get("max_iterations", 10),
            max_execution_time=self.config.get("max_execution_time", 300),
        )

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the KPIT agent.

        Args:
            input_data: Must contain a 'query' key with the automation task

        Returns:
            Dict containing:
                - query: Original query
                - response: Agent's response
                - tool_calls: List of tools used and their results
                - metadata: Additional information about the execution

        Raises:
            ValueError: If input_data is not a dict or missing query
        """
        if not isinstance(input_data, dict):
            raise ValueError("Input data must be a dictionary")

        query = input_data.get("query")
        if not query:
            raise ValueError("Input must contain a 'query' key")

        result = self.agent.invoke(
            {
                "input": f"KPIT automation task: {query}\n\nIMPORTANT: You must use your tools to gather information before responding.",
            }
        )

        return {
            "query": query,
            "response": result["output"],
            "tool_calls": result.get("intermediate_steps", []),
            "metadata": {
                "tools_used": [tool.name for tool in self.tools],
                "model": getattr(self.model, 'model_name', 'mistral'),
            },
        }
