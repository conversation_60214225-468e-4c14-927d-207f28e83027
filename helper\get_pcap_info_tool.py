"""Get PCAP Info Tool - Get detailed information about a specific PCAP file.

This tool retrieves detailed metadata about a PCAP file including size, timestamps,
permissions, and other file system information.
"""

import j<PERSON>
from typing import Any, Dict
from base_tool import <PERSON>Tool
from file_tools import get_pcap_file_info, FileSystemError


class GetPCAPInfoTool(BaseTool):
    """Tool for getting detailed information about a specific PCAP file."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Get PCAP Info tool.

        Args:
            config (dict): Configuration containing:
                - pcap_folder (str): Path to PCAP files folder
        """
        super().__init__(config)
        
        if not self.config or 'pcap_folder' not in self.config:
            raise ValueError("Configuration with 'pcap_folder' is required")
        
        self.pcap_folder = self.config['pcap_folder']

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_pcap_info"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get detailed information about a specific PCAP file. "
                "Input should contain 'filename' key.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get PCAP file information.

        Args:
            input_data (dict): Input parameters containing:
                - filename (str): Name of the PCAP file

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - info (dict): PCAP file information (if successful)
                - error (str): Error message (if failed)
        """
        try:
            if 'filename' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'filename'"
                }
            
            filename = input_data['filename']
            
            # Remove quotes that might be added by LLM
            filename = filename.strip().strip("'\"")
            
            if not filename:
                return {
                    'success': False,
                    'error': "Filename cannot be empty"
                }
            
            # Get PCAP file information
            info = get_pcap_file_info(filename, self.pcap_folder)
            
            return {
                'success': True,
                'info': info,
                'filename': filename,
                'folder': self.pcap_folder
            }
            
        except FileSystemError as e:
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): PCAP filename

        Returns:
            str: JSON string with file information or error message
        """
        try:
            # Treat input as filename string
            result = self.run({'filename': input_str})
            
            if result['success']:
                return json.dumps(result['info'], indent=2)
            else:
                return f"Error getting PCAP info: {result['error']}"
                
        except Exception as e:
            return f"Error: {str(e)}"
