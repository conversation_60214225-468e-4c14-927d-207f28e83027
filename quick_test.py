#!/usr/bin/env python3
"""Quick test to verify KPIT agent structure."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all imports work correctly."""
    try:
        print("Testing imports...")
        
        # Test agent import
        from ai_agents.agents.kpit_agent import KPITAgent
        print("✅ KPITAgent import successful")
        
        # Test tools import
        from ai_agents.agents.kpit_agent.tools import (
            GitLabFileSHATool,
            GitLabFileContentTool,
            SQLDatabaseListTablesTool,
            ListLogFilesTool
        )
        print("✅ Tools import successful")
        
        # Test registry
        from ai_agents.agents.registry import AGENT_REGISTRY
        if "KPIT_agent" in AGENT_REGISTRY:
            print("✅ KPIT_agent registered in registry")
        else:
            print("❌ KPIT_agent not found in registry")
            return False
        
        # Test tool instantiation (without config)
        try:
            tool = GitLabFileSHATool()
            print(f"✅ GitLab tool created: {tool.name}")
        except Exception as e:
            print(f"⚠️  GitLab tool creation (expected without config): {e}")
        
        try:
            tool = ListLogFilesTool()
            print(f"✅ Filesystem tool created: {tool.name}")
        except Exception as e:
            print(f"❌ Filesystem tool creation failed: {e}")
            return False
        
        print("\n🎉 All imports and basic structure tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """Test config loading."""
    try:
        print("\nTesting config loading...")
        from ai_agents.agents.kpit_agent import KPITAgent
        
        config_path = "src/ai_agents/agents/kpit_agent/config.yaml"
        if os.path.exists(config_path):
            config = KPITAgent.load_config(config_path)
            print(f"✅ Config loaded successfully")
            print(f"   Model: {config.get('model', {}).get('model_id', 'Not specified')}")
            print(f"   GitLab project: {config.get('gitlab', {}).get('project_id', 'Not specified')}")
            print(f"   SQL enabled: {config.get('sql', {}).get('enabled', False)}")
            return True
        else:
            print(f"❌ Config file not found: {config_path}")
            return False
            
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 KPIT Agent Structure Test")
    print("=" * 40)
    
    success = True
    success &= test_imports()
    success &= test_config_loading()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ All tests passed! KPIT Agent structure is correct.")
        print("\nNext steps:")
        print("1. Install dependencies: poetry install")
        print("2. Set up .env file with API keys")
        print("3. Test with: python -m ai_agents.execution.cli KPIT_agent --input '{\"query\": \"test\"}'")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
