"""GitLab Tools for KPIT Agent."""

import json
import requests
from typing import Any, Dict
from langchain_core.tools import BaseTool
from datetime import datetime


class GitLabAPIError(Exception):
    """Custom exception for GitLab API errors."""
    pass


def make_gitlab_tools(config: Dict[str, Any]):
    """Create GitLab API functions."""
    gitlab_url = config['gitlab_url']
    project_id = config['project_id']
    token = config['token']
    
    headers = {'PRIVATE-TOKEN': token}
    
    def get_file_sha(commit_ref: str, file_path: str) -> str:
        """Get SHA hash of a file at specific commit reference."""
        url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/files/{file_path.replace('/', '%2F')}"
        params = {'ref': commit_ref}
        
        response = requests.get(url, headers=headers, params=params)
        if response.status_code != 200:
            raise GitLabAPIError(f"Failed to get file SHA: {response.status_code} - {response.text}")
        
        return response.json()['blob_id']
    
    def get_file_sha_from_timestamp(file_path: str, timestamp: str) -> str:
        """Get SHA hash of a file at specific timestamp."""
        # Convert timestamp to datetime
        try:
            target_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError:
            raise GitLabAPIError(f"Invalid timestamp format: {timestamp}")
        
        # Get commits for the file
        url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/commits"
        params = {'path': file_path, 'since': target_time.isoformat()}
        
        response = requests.get(url, headers=headers, params=params)
        if response.status_code != 200:
            raise GitLabAPIError(f"Failed to get commits: {response.status_code} - {response.text}")
        
        commits = response.json()
        if not commits:
            raise GitLabAPIError(f"No commits found for file {file_path} after {timestamp}")
        
        # Get the oldest commit (closest to timestamp)
        oldest_commit = commits[-1]
        return get_file_sha(oldest_commit['id'], file_path)
    
    def get_file_content(file_sha: str) -> str:
        """Get file content using SHA hash."""
        url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/blobs/{file_sha}"
        
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise GitLabAPIError(f"Failed to get file content: {response.status_code} - {response.text}")
        
        blob_data = response.json()
        if blob_data.get('encoding') == 'base64':
            import base64
            return base64.b64decode(blob_data['content']).decode('utf-8')
        else:
            return blob_data['content']
    
    return get_file_sha, get_file_sha_from_timestamp, get_file_content


class GitLabFileSHATool(BaseTool):
    """Tool for getting SHA hash of a file at a specific commit reference."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the GitLab File SHA tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config:
            required_keys = ['gitlab_url', 'project_id', 'token']
            for key in required_keys:
                if key not in self.config:
                    raise ValueError(f"Missing required configuration key: {key}")
            
            self.get_sha_func, _, _ = make_gitlab_tools(self.config)
        else:
            self.get_sha_func = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_sha"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get SHA hash of a file at a specific commit reference. "
                "Input should be JSON with 'commit_ref' and 'file_path' keys.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.get_sha_func is None:
                return "Tool not configured. Please provide GitLab configuration."
            
            # Parse JSON input
            try:
                params = json.loads(input_str)
            except json.JSONDecodeError:
                return f"Error parsing input: Expected JSON with 'commit_ref' and 'file_path' keys."
            
            if 'commit_ref' not in params or 'file_path' not in params:
                return "Missing required parameters: 'commit_ref' and 'file_path'"
            
            sha = self.get_sha_func(params['commit_ref'], params['file_path'])
            return sha
            
        except GitLabAPIError as e:
            return f"GitLab API Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class GitLabFileContentTool(BaseTool):
    """Tool for getting file content using SHA hash."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the GitLab File Content tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config:
            required_keys = ['gitlab_url', 'project_id', 'token']
            for key in required_keys:
                if key not in self.config:
                    raise ValueError(f"Missing required configuration key: {key}")
            
            _, _, self.get_content_func = make_gitlab_tools(self.config)
        else:
            self.get_content_func = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_content"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get file content using SHA hash. "
                "Input should be JSON with 'file_sha' key.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.get_content_func is None:
                return "Tool not configured. Please provide GitLab configuration."
            
            # Parse JSON input
            try:
                params = json.loads(input_str)
            except json.JSONDecodeError:
                return f"Error parsing input: Expected JSON with 'file_sha' key."
            
            if 'file_sha' not in params:
                return "Missing required parameter: 'file_sha'"
            
            content = self.get_content_func(params['file_sha'])
            return content
            
        except GitLabAPIError as e:
            return f"GitLab API Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class GitLabFileSHAFromTimestampTool(BaseTool):
    """Tool for getting SHA hash of a file at a specific timestamp."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the GitLab File SHA from Timestamp tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config:
            required_keys = ['gitlab_url', 'project_id', 'token']
            for key in required_keys:
                if key not in self.config:
                    raise ValueError(f"Missing required configuration key: {key}")
            
            _, self.get_sha_from_timestamp_func, _ = make_gitlab_tools(self.config)
        else:
            self.get_sha_from_timestamp_func = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_sha_from_timestamp"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get SHA hash of a file at a specific timestamp. "
                "Input should be JSON with 'file_path' and 'timestamp' keys.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.get_sha_from_timestamp_func is None:
                return "Tool not configured. Please provide GitLab configuration."
            
            # Parse JSON input
            try:
                params = json.loads(input_str)
            except json.JSONDecodeError:
                return f"Error parsing input: Expected JSON with 'file_path' and 'timestamp' keys."
            
            if 'file_path' not in params or 'timestamp' not in params:
                return "Missing required parameters: 'file_path' and 'timestamp'"
            
            sha = self.get_sha_from_timestamp_func(params['file_path'], params['timestamp'])
            return sha
            
        except GitLabAPIError as e:
            return f"GitLab API Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"
