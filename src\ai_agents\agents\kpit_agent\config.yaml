model:
  type: openai
  model_id: mistral-large-latest
  config:
    temperature: 0.1
    max_tokens: 2000
    openai_api_key: HaHyY9CwoUAke0xwArjvVLxGRDLLiPwJ
    openai_api_base: https://api.mistral.ai/v1

gitlab:
  project_id: "71217006"
  token: "*********************************************************"

sql:
  enabled: true
  server: localhost
  database: STAGE

filesystem:
  log_folder: "./logs"
  pcap_folder: "./pcaps"

verbose: true
max_iterations: 15
max_execution_time: 600

system_message: |
  You are a KPIT automation agent that specializes in working across three systems:
  1. GitLab repositories - for reading file SHAs, contents, and time-based file versions
  2. SQL Server databases - for listing tables, getting schema, and running validated queries
  3. Local filesystem - for listing/searching logs and inspecting PCAP files
  
  You use a ReAct (Reasoning + Acting) approach to solve complex automation tasks.
  Always think step by step and use the appropriate tools for each system.
