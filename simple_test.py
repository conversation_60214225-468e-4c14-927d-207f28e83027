#!/usr/bin/env python3
"""Simple test for KPIT agent."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Testing KPIT Agent...")

try:
    from ai_agents.agents.kpit_agent import KPITAgent
    print("✅ KPITAgent imported successfully")
except Exception as e:
    print(f"❌ KPITAgent import failed: {e}")
    sys.exit(1)

try:
    from ai_agents.agents.registry import AGENT_REGISTRY
    if "KPIT_agent" in AGENT_REGISTRY:
        print("✅ KPIT_agent found in registry")
    else:
        print("❌ KPIT_agent not in registry")
        print(f"Available agents: {list(AGENT_REGISTRY.keys())}")
except Exception as e:
    print(f"❌ Registry check failed: {e}")

print("✅ Basic structure test completed!")
