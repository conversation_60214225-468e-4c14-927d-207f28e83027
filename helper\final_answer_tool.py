"""Final Answer Tool - Provide final answer for SQL operations.

This tool is used to provide the final answer to the user and stop the agent
execution, particularly useful for SQL operations where a definitive result
needs to be returned.
"""

import j<PERSON>
from typing import Any, Dict
from base_tool import BaseTool


class FinalAnswerTool(BaseTool):
    """Tool for providing final answers and stopping agent execution."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Final Answer tool.

        Args:
            config (dict, optional): Tool configuration (not required for this tool)
        """
        super().__init__(config)
        # This tool doesn't require configuration

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "final_answer_tool"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Use this tool to provide the final answer to the user and stop. "
                "Input should contain 'answer' key with the final response.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to provide final answer.

        Args:
            input_data (dict): Input parameters containing:
                - answer (str): Final answer to provide to the user

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - final_answer (str): Final answer (if successful)
                - error (str): Error message (if failed)
        """
        try:
            if 'answer' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'answer'"
                }
            
            answer = input_data['answer']
            
            if not answer:
                return {
                    'success': False,
                    'error': "Answer cannot be empty"
                }
            
            return {
                'success': True,
                'final_answer': answer,
                'stop_execution': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Error providing final answer: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): Final answer string

        Returns:
            str: Final answer (this should stop agent execution)
        """
        try:
            # Treat input as final answer string
            result = self.run({'answer': input_str})
            
            if result['success']:
                return result['final_answer']
            else:
                return result['error']
                
        except Exception as e:
            return f"Error: {str(e)}"
