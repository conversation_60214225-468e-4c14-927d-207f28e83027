# KPIT Agent

A ReAct-style autonomous agent that reasons and acts across three systems:
- **GitLab repositories** (read file SHAs and contents, time-based file versions)
- **SQL Server databases** (list tables, get schema, validate/run queries)
- **Local filesystem** (list/search logs, list/inspect PCAPs)

Uses <PERSON><PERSON><PERSON><PERSON>'s ReAct loop with the Mistral LLM to plan tool calls and synthesize answers.

## Features

### GitLab Operations
- Get SHA hash of files at specific commit references
- Retrieve file content using SHA hashes
- Get file versions based on timestamps
- Works with GitLab API v4

### SQL Server Operations
- List all tables in the database
- Get schema information for specific tables
- Validate SQL queries before execution
- Execute SQL queries safely
- Uses LangChain SQL toolkit for robust database operations

### Filesystem Operations
- List log files with metadata (size, timestamps)
- Search for patterns in log files
- Get content of specific log files
- List PCAP files with detailed information
- Get metadata about PCAP files

## Configuration

The agent is configured via `config.yaml`:

```yaml
model:
  type: openai
  model_id: mistral-large-latest
  config:
    temperature: 0.1
    max_tokens: 2000
    openai_api_key: "your-mistral-api-key"
    openai_api_base: "https://api.mistral.ai/v1"

gitlab:
  project_id: "71217006"
  token: "your-gitlab-token"

sql:
  enabled: true
  server: localhost
  database: STAGE

filesystem:
  log_folder: "./logs"
  pcap_folder: "./pcaps"
```

## Usage

### Via CLI
```bash
python -m ai_agents.execution.cli KPIT_agent --input '{"query": "List all tables in the database"}'
```

### Via Python
```python
from ai_agents.agents.registry import get_agent

agent = get_agent("KPIT_agent")
result = agent.run({"query": "Get the content of README.md from the main branch"})
print(result['response'])
```

## Example Queries

### GitLab Operations
- "Get the SHA hash of README.md from the main branch"
- "Show me the content of config.yaml file"
- "Get the version of setup.py from 2023-12-01"

### SQL Operations
- "List all tables in the database"
- "Show me the schema for the users table"
- "Count the number of records in the orders table"

### Filesystem Operations
- "List all log files in the logs folder"
- "Search for 'ERROR' in all log files"
- "Show me the last 50 lines of application.log"
- "List all PCAP files and their sizes"

## Tools Available

### GitLab Tools
1. **get_file_sha** - Get SHA hash of a file at specific commit reference
2. **get_file_content** - Get file content using SHA hash
3. **get_file_sha_from_timestamp** - Get file SHA at specific timestamp

### SQL Tools
1. **sql_db_list_tables** - List all database tables
2. **sql_db_schema** - Get schema for specific tables
3. **sql_db_query_checker** - Validate SQL queries
4. **sql_db_query** - Execute SQL queries

### Filesystem Tools
1. **list_log_files** - List log files with metadata
2. **search_logs** - Search patterns in log files
3. **get_log_content** - Get content of log files
4. **list_pcap_files** - List PCAP files
5. **get_pcap_info** - Get PCAP file information

## ReAct Pattern

The agent follows the ReAct (Reasoning + Acting) pattern:

1. **Thought** - Analyzes the query and plans the approach
2. **Action** - Selects and executes appropriate tools
3. **Observation** - Reviews the results from tool execution
4. **Repeat** - Continues until the task is complete
5. **Final Answer** - Provides a comprehensive response

## Error Handling

The agent includes robust error handling for:
- GitLab API connection issues
- SQL Server connection problems
- File system access errors
- Invalid input parameters
- Network timeouts

## Security Considerations

- GitLab tokens are used for API authentication
- SQL Server uses trusted connections (Windows Authentication)
- File system access is restricted to configured folders
- All inputs are validated before execution
