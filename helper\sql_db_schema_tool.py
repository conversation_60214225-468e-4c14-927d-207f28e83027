"""SQL Database Schema Tool - Get database schema information.

This tool retrieves schema information for specified database tables including
column definitions, data types, constraints, and sample data.
"""

import json
from typing import Any, Dict, Optional
from base_tool import BaseTool
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit


class SQLDatabaseSchemaTool(BaseTool):
    """Tool for getting database schema information."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the SQL Database Schema tool.

        Args:
            config (dict): Configuration containing:
                - db (SQLDatabase): LangChain SQLDatabase instance
                - llm: Language model for SQL operations
        """
        super().__init__(config)
        
        if not self.config:
            raise ValueError("Configuration is required")
        
        if 'db' not in self.config:
            raise ValueError("Database instance 'db' is required in configuration")
        
        self.db = self.config['db']
        self.llm = self.config.get('llm')
        
        # Initialize SQL toolkit to get the schema tool
        if self.llm:
            toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
            sql_tools = toolkit.get_tools()
            
            # Find the schema tool
            self.schema_tool = None
            for tool in sql_tools:
                if tool.name == "sql_db_schema":
                    self.schema_tool = tool
                    break
            
            if not self.schema_tool:
                raise ValueError("Could not find sql_db_schema tool in toolkit")
        else:
            raise ValueError("Language model 'llm' is required in configuration")

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_schema"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get database schema information for specified tables. "
                "Input should contain 'table_names' key with comma-separated table names.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get database schema.

        Args:
            input_data (dict): Input parameters containing:
                - table_names (str): Comma-separated list of table names

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - schema (str): Database schema information (if successful)
                - error (str): Error message (if failed)
        """
        try:
            if 'table_names' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'table_names'"
                }
            
            table_names = input_data['table_names']
            
            # Remove quotes that might be added by LLM
            table_names = table_names.strip().strip("'\"")
            
            if not table_names:
                return {
                    'success': False,
                    'error': "Table names cannot be empty"
                }
            
            # Get schema using the SQL toolkit tool
            schema = self.schema_tool._run(table_names)
            
            return {
                'success': True,
                'schema': schema,
                'table_names': table_names
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Error getting schema: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): Comma-separated table names

        Returns:
            str: Database schema information or error message
        """
        try:
            # Treat input as table names string
            result = self.run({'table_names': input_str})
            
            if result['success']:
                return result['schema']
            else:
                return result['error']
                
        except Exception as e:
            return f"Error: {str(e)}"
