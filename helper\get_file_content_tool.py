"""GitLab File Content Tool - Get file content using SHA hash.

This tool retrieves the content of a file from a GitLab repository using its SHA hash.
"""

import json
from typing import Any, Dict
from base_tool import BaseTool
from tools import make_gitlab_tools, GitLabAPIError


class GetFileContentTool(BaseTool):
    """Tool for getting file content using SHA hash."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the GitLab File Content tool.

        Args:
            config (dict): Configuration containing GitLab settings:
                - gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
                - project_id: GitLab project ID
                - token: GitLab access token
        """
        super().__init__(config)
        
        if not self.config:
            raise ValueError("GitLab configuration is required")
        
        required_keys = ['gitlab_url', 'project_id', 'token']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        # Initialize GitLab tools
        _, _, self.get_content_func = make_gitlab_tools(self.config)

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_content"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get file content using SHA hash. "
                "Input should contain 'file_sha' key.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get file content.

        Args:
            input_data (dict): Input parameters containing:
                - file_sha (str): SHA hash of the file

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - content (str): File content (if successful)
                - error (str): Error message (if failed)
        """
        try:
            # Validate input
            if 'file_sha' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'file_sha'"
                }
            
            file_sha = input_data['file_sha']
            
            if not file_sha or not file_sha.strip():
                return {
                    'success': False,
                    'error': "file_sha cannot be empty"
                }
            
            # Get file content using GitLab API
            content = self.get_content_func(file_sha)
            
            return {
                'success': True,
                'content': content,
                'file_sha': file_sha
            }
            
        except GitLabAPIError as e:
            return {
                'success': False,
                'error': f"GitLab API Error: {str(e)}"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a JSON string input (for LangChain compatibility).

        Args:
            input_str (str): JSON string with 'file_sha' key
                Example: '{"file_sha": "abc123def456789..."}'

        Returns:
            str: File content or error message
        """
        try:
            params = json.loads(input_str)
            result = self.run(params)
            
            if result['success']:
                return result['content']
            else:
                return result['error']
                
        except json.JSONDecodeError as e:
            return f"Error parsing input: {str(e)}. Expected JSON with 'file_sha' key."
        except Exception as e:
            return f"Error: {str(e)}"
