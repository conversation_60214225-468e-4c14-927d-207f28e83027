#!/usr/bin/env python3
"""Example usage of KPIT Agent."""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_agents.agents.registry import get_agent


def main():
    """Example usage of KPIT Agent."""
    
    # Create .env file if it doesn't exist
    env_file = ".env"
    if not os.path.exists(env_file):
        with open(env_file, 'w') as f:
            f.write("PYTHONPATH=src\n")
            f.write("OPENAI_API_KEY=HaHyY9CwoUAke0xwArjvVLxGRDLLiPwJ\n")
        print(f"Created {env_file} file")
    
    try:
        # Get the KPIT agent
        agent = get_agent("KPIT_agent")
        
        print("🤖 KPIT Agent initialized successfully!")
        print(f"📋 Available tools: {len(agent.tools)}")
        for tool in agent.tools:
            print(f"   - {tool.name}: {tool.description}")
        
        # Example queries
        examples = [
            "What tools do I have available for GitLab operations?",
            "How can I list tables in the SQL database?",
            "What filesystem operations can I perform?",
        ]
        
        for i, query in enumerate(examples, 1):
            print(f"\n{'='*60}")
            print(f"Example {i}: {query}")
            print('='*60)
            
            try:
                result = agent.run({"query": query})
                print(f"Response: {result['response']}")
                
                if result['tool_calls']:
                    print(f"Tools used: {len(result['tool_calls'])} tool calls")
                
            except Exception as e:
                print(f"Error: {str(e)}")
        
        print(f"\n{'='*60}")
        print("🎯 KPIT Agent is ready for automation tasks!")
        print("You can now use it via CLI:")
        print("python -m ai_agents.execution.cli KPIT_agent --input '{\"query\": \"your task here\"}'")
        print('='*60)
        
    except Exception as e:
        print(f"❌ Error initializing KPIT agent: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
