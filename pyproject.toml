[tool.poetry]
name = 'ai-agents'
version = '0.1.0'



[tool.poetry.dependencies]
python = ">=3.9,<3.9.7 || >3.9.7"
streamlit = ">=1.28.0,<2.0.0"
langchain = ">=0.3.0,<0.4.0"
langchain-community = ">=0.3.0,<0.4.0"
langchain-core = ">=0.3.0,<0.4.0"
langchain-openai = ">=0.2.0,<0.4.0"
pydantic = ">=2.0.0,<3.0.0"
openai = ">=1.3.0,<2.0.0"
python-dotenv = ">=1.0.0,<2.0.0"
typing-extensions = ">=4.8.0,<5.0.0"
pydantic-settings = "^2.0.0"
openpyxl = "*"
pyodbc = "*"
langgraph = "*"
redis = "*"
graphviz = "*"
duckduckgo-search = "*"
wikipedia = "*"

[tool.poetry.group.dev.dependencies]
python = ">=3.9,<3.9.7 || >3.9.7"
sphinx = "6.0.0"
black = "22.3.0"
coverage = {extras = ["toml"], version = "6.2"}
flake8 = "4.0.1"
flake8-bugbear = "21.9.2"
flake8-docstrings = "1.6.0"
flake8-rst-docstrings = "0.2.5"
flake8-html = "0.4.2"
isort = "*"
mypy = "0.930"
pep8-naming = "0.12.1"
pre-commit = "2.16.0"
pre-commit-hooks = "4.1.0"
pytest = "6.2.5"
pyupgrade = "2.29.1"
safety = "1.10.3"
sphinx-autobuild = "2021.3.14"
sphinx-click = "3.0.2"
sphinx-rtd-theme = "1.2.2"
ruff = "*"




[tool.coverage.paths]
source = ["src", "*/site-packages"]
tests = ["tests", "*/tests"]

[tool.coverage.run]
branch = true
source = ["autogeny", "tests"]

[tool.coverage.report]
show_missing = true
fail_under = 30

[tool.isort]
profile = "black"
force_single_line = true
lines_after_imports = 2

[tool.mypy]
strict = true
warn_unreachable = true
pretty = true
show_column_numbers = true
show_error_context = true

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
lint.select = ["D205"]
