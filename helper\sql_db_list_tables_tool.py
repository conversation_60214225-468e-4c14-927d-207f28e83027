"""SQL Database List Tables Tool - List all tables in the database.

This tool retrieves a list of all available tables in the database.
"""

import j<PERSON>
from typing import Any, Dict
from base_tool import BaseTool
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit


class SQLDatabaseListTablesTool(BaseTool):
    """Tool for listing all database tables."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the SQL Database List Tables tool.

        Args:
            config (dict): Configuration containing:
                - db (SQLDatabase): LangChain SQLDatabase instance
                - llm: Language model for SQL operations
        """
        super().__init__(config)
        
        if not self.config:
            raise ValueError("Configuration is required")
        
        if 'db' not in self.config:
            raise ValueError("Database instance 'db' is required in configuration")
        
        self.db = self.config['db']
        self.llm = self.config.get('llm')
        
        # Initialize SQL toolkit to get the list tables tool
        if self.llm:
            toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
            sql_tools = toolkit.get_tools()
            
            # Find the list tables tool
            self.list_tables_tool = None
            for tool in sql_tools:
                if tool.name == "sql_db_list_tables":
                    self.list_tables_tool = tool
                    break
            
            if not self.list_tables_tool:
                raise ValueError("Could not find sql_db_list_tables tool in toolkit")
        else:
            raise ValueError("Language model 'llm' is required in configuration")

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_list_tables"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("List all tables in the database. "
                "Input should be an empty string or no input required.")

    def run(self, input_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute the tool to list database tables.

        Args:
            input_data (dict, optional): Input parameters (not used for this tool)

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - tables (str): Comma-separated list of table names (if successful)
                - error (str): Error message (if failed)
        """
        try:
            # List tables using the SQL toolkit tool
            tables = self.list_tables_tool._run("")
            
            return {
                'success': True,
                'tables': tables
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Error listing tables: {str(e)}"
            }

    def run_from_string(self, input_str: str = "") -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): Empty string (not used for this tool)

        Returns:
            str: Comma-separated list of table names or error message
        """
        try:
            result = self.run()
            
            if result['success']:
                return result['tables']
            else:
                return result['error']
                
        except Exception as e:
            return f"Error: {str(e)}"
