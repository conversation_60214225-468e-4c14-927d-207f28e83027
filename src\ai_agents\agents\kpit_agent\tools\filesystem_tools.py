"""Filesystem Tools for KPIT Agent."""

import json
import os
import glob
import re
from typing import Any, Dict, List
from langchain_core.tools import BaseTool
from datetime import datetime


class FileSystemError(Exception):
    """Custom exception for filesystem errors."""
    pass


def list_log_files(log_folder: str, pattern: str = "*.log") -> List[Dict[str, Any]]:
    """List log files in the specified folder."""
    if not os.path.exists(log_folder):
        raise FileSystemError(f"Log folder does not exist: {log_folder}")
    
    search_pattern = os.path.join(log_folder, pattern)
    files = glob.glob(search_pattern)
    
    file_list = []
    for file_path in files:
        try:
            stat = os.stat(file_path)
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            }
            file_list.append(file_info)
        except OSError as e:
            continue  # Skip files that can't be accessed
    
    return sorted(file_list, key=lambda x: x['modified'], reverse=True)


def search_logs(log_folder: str, pattern: str, file_pattern: str = "*.log") -> List[Dict[str, Any]]:
    """Search for patterns in log files."""
    if not os.path.exists(log_folder):
        raise FileSystemError(f"Log folder does not exist: {log_folder}")
    
    search_pattern_files = os.path.join(log_folder, file_pattern)
    files = glob.glob(search_pattern_files)
    
    results = []
    regex = re.compile(pattern, re.IGNORECASE)
    
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    if regex.search(line):
                        results.append({
                            'file': os.path.basename(file_path),
                            'path': file_path,
                            'line_number': line_num,
                            'line': line.strip(),
                        })
        except (OSError, UnicodeDecodeError):
            continue  # Skip files that can't be read
    
    return results


def get_log_content(log_folder: str, filename: str, lines: int = None) -> str:
    """Get content of a log file."""
    file_path = os.path.join(log_folder, filename)
    
    if not os.path.exists(file_path):
        raise FileSystemError(f"Log file does not exist: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            if lines is None:
                return f.read()
            else:
                # Read last N lines
                all_lines = f.readlines()
                return ''.join(all_lines[-lines:])
    except OSError as e:
        raise FileSystemError(f"Error reading log file: {str(e)}")


def list_pcap_files(pcap_folder: str) -> List[Dict[str, Any]]:
    """List PCAP files in the specified folder."""
    if not os.path.exists(pcap_folder):
        raise FileSystemError(f"PCAP folder does not exist: {pcap_folder}")
    
    patterns = ["*.pcap", "*.pcapng", "*.cap"]
    files = []
    
    for pattern in patterns:
        search_pattern = os.path.join(pcap_folder, pattern)
        files.extend(glob.glob(search_pattern))
    
    file_list = []
    for file_path in files:
        try:
            stat = os.stat(file_path)
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'extension': os.path.splitext(file_path)[1],
            }
            file_list.append(file_info)
        except OSError:
            continue  # Skip files that can't be accessed
    
    return sorted(file_list, key=lambda x: x['modified'], reverse=True)


def get_pcap_info(pcap_folder: str, filename: str) -> Dict[str, Any]:
    """Get basic information about a PCAP file."""
    file_path = os.path.join(pcap_folder, filename)
    
    if not os.path.exists(file_path):
        raise FileSystemError(f"PCAP file does not exist: {file_path}")
    
    try:
        stat = os.stat(file_path)
        return {
            'name': filename,
            'path': file_path,
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'extension': os.path.splitext(file_path)[1],
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
        }
    except OSError as e:
        raise FileSystemError(f"Error getting PCAP info: {str(e)}")


class ListLogFilesTool(BaseTool):
    """Tool for listing log files in the configured folder."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the List Log Files tool."""
        super().__init__()
        self.config = config or {}
        self.log_folder = self.config.get('log_folder', './logs')

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "list_log_files"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("List log files in the configured folder. "
                "Input should be file pattern (e.g., '*.log', '*.txt') or empty for default.")

    def _run(self, input_str: str = "*.log") -> str:
        """Execute the tool."""
        try:
            pattern = input_str.strip() if input_str.strip() else "*.log"
            pattern = pattern.strip("'\"")  # Remove quotes
            
            files = list_log_files(self.log_folder, pattern)
            return json.dumps(files, indent=2)
            
        except FileSystemError as e:
            return f"Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class SearchLogsTool(BaseTool):
    """Tool for searching patterns in log files."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the Search Logs tool."""
        super().__init__()
        self.config = config or {}
        self.log_folder = self.config.get('log_folder', './logs')

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "search_logs"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Search for patterns in log files. "
                "Input should be JSON with 'pattern' and optional 'file_pattern' keys.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            # Try to parse as JSON first
            try:
                params = json.loads(input_str)
                pattern = params.get('pattern')
                file_pattern = params.get('file_pattern', '*.log')
            except json.JSONDecodeError:
                # Treat as simple pattern string
                pattern = input_str.strip().strip("'\"")
                file_pattern = '*.log'
            
            if not pattern:
                return "Error: Search pattern is required"
            
            results = search_logs(self.log_folder, pattern, file_pattern)
            return json.dumps(results, indent=2)
            
        except FileSystemError as e:
            return f"Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class GetLogContentTool(BaseTool):
    """Tool for getting content of a log file."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the Get Log Content tool."""
        super().__init__()
        self.config = config or {}
        self.log_folder = self.config.get('log_folder', './logs')

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_log_content"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get content of a log file. "
                "Input should be JSON with 'filename' and optional 'lines' keys.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            # Try to parse as JSON first
            try:
                params = json.loads(input_str)
                filename = params.get('filename')
                lines = params.get('lines')
            except json.JSONDecodeError:
                # Treat as filename string
                filename = input_str.strip().strip("'\"")
                lines = None
            
            if not filename:
                return "Error: Filename is required"
            
            content = get_log_content(self.log_folder, filename, lines)
            return content
            
        except FileSystemError as e:
            return f"Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class ListPCAPFilesTool(BaseTool):
    """Tool for listing PCAP files in the configured folder."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the List PCAP Files tool."""
        super().__init__()
        self.config = config or {}
        self.pcap_folder = self.config.get('pcap_folder', './pcaps')

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "list_pcap_files"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return "List PCAP files in the configured folder. No input required."

    def _run(self, input_str: str = "") -> str:
        """Execute the tool."""
        try:
            files = list_pcap_files(self.pcap_folder)
            return json.dumps(files, indent=2)
            
        except FileSystemError as e:
            return f"Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"


class GetPCAPInfoTool(BaseTool):
    """Tool for getting information about a PCAP file."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the Get PCAP Info tool."""
        super().__init__()
        self.config = config or {}
        self.pcap_folder = self.config.get('pcap_folder', './pcaps')

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_pcap_info"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get information about a PCAP file. "
                "Input should be the filename.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            filename = input_str.strip().strip("'\"")
            
            if not filename:
                return "Error: Filename is required"
            
            info = get_pcap_info(self.pcap_folder, filename)
            return json.dumps(info, indent=2)
            
        except FileSystemError as e:
            return f"Error: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"
