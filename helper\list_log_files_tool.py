"""List Log Files Tool - List log files in configured folder.

This tool lists log files (*.log, *.txt, *.out, *.err) in the configured folder
with detailed metadata including size, timestamps, and file information.
"""

import j<PERSON>
from typing import Any, Dict, List
from base_tool import BaseTool
from file_tools import list_log_files, FileSystemError


class ListLogFilesTool(BaseTool):
    """Tool for listing log files in the configured folder."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the List Log Files tool.

        Args:
            config (dict): Configuration containing:
                - log_folder (str): Path to log files folder
        """
        super().__init__(config)
        
        if not self.config or 'log_folder' not in self.config:
            raise ValueError("Configuration with 'log_folder' is required")
        
        self.log_folder = self.config['log_folder']

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "list_log_files"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("List log files in the configured folder. "
                "Input should contain 'pattern' key (e.g., '*.log', '*.txt').")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to list log files.

        Args:
            input_data (dict): Input parameters containing:
                - pattern (str, optional): File pattern to match (default: "*.log")

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - files (list): List of log file information (if successful)
                - count (int): Number of files found
                - error (str): Error message (if failed)
        """
        try:
            pattern = input_data.get('pattern', '*.log')
            
            # Remove quotes that might be added by LLM
            pattern = pattern.strip().strip("'\"") if pattern.strip() else "*.log"
            
            # List log files
            files = list_log_files(self.log_folder, pattern)
            
            return {
                'success': True,
                'files': files,
                'count': len(files),
                'pattern': pattern,
                'folder': self.log_folder
            }
            
        except FileSystemError as e:
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): File pattern (e.g., "*.log", "*.txt")

        Returns:
            str: JSON string with file list or error message
        """
        try:
            # Treat input as pattern string
            result = self.run({'pattern': input_str})
            
            if result['success']:
                return json.dumps(result['files'], indent=2)
            else:
                return f"Error listing log files: {result['error']}"
                
        except Exception as e:
            return f"Error: {str(e)}"
