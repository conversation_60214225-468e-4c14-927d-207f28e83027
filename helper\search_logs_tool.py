"""Search Logs Tool - Search for a term across all log files.

This tool searches for specific terms across all log files in the configured folder
and returns matching lines with context information.
"""

import json
from typing import Any, Dict
from base_tool import BaseTool
from file_tools import search_log_files, FileSystemError


class SearchLogsTool(BaseTool):
    """Tool for searching across all log files."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Search Logs tool.

        Args:
            config (dict): Configuration containing:
                - log_folder (str): Path to log files folder
                - max_search_results (int, optional): Default maximum results to return
        """
        super().__init__(config)
        
        if not self.config or 'log_folder' not in self.config:
            raise ValueError("Configuration with 'log_folder' is required")
        
        self.log_folder = self.config['log_folder']
        self.default_max_results = self.config.get('max_search_results', 100)

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "search_logs"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Search for a term across all log files. Input should contain 'search_term' "
                "and optional 'max_results' keys, or just the search term.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to search across log files.

        Args:
            input_data (dict): Input parameters containing:
                - search_term (str): Term to search for
                - max_results (int, optional): Maximum results to return (default: 100)

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - results (list): Search results (if successful)
                - count (int): Number of results found
                - error (str): Error message (if failed)
        """
        try:
            if 'search_term' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'search_term'"
                }
            
            search_term = input_data['search_term']
            max_results = input_data.get('max_results', self.default_max_results)
            
            # Remove quotes that might be added by LLM
            search_term = search_term.strip().strip("'\"")
            
            if not search_term:
                return {
                    'success': False,
                    'error': "Search term cannot be empty"
                }
            
            # Search across log files
            results = search_log_files(search_term, self.log_folder, max_results)
            
            return {
                'success': True,
                'results': results,
                'count': len(results),
                'search_term': search_term,
                'max_results': max_results,
                'folder': self.log_folder
            }
            
        except FileSystemError as e:
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): JSON string with parameters or just search term

        Returns:
            str: JSON string with search results or error message
        """
        try:
            # Try to parse as JSON first
            if input_str.startswith('{'):
                params = json.loads(input_str)
                result = self.run(params)
            else:
                # Treat as search term string
                result = self.run({'search_term': input_str})
            
            if result['success']:
                return json.dumps(result['results'], indent=2)
            else:
                return f"Error searching logs: {result['error']}"
                
        except json.JSONDecodeError as e:
            return f"Error parsing input: {str(e)}. Expected JSON with 'search_term' key or just search term string."
        except Exception as e:
            return f"Error: {str(e)}"
