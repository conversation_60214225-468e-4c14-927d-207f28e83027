#!/usr/bin/env python3
"""Test basic imports."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Testing basic imports...")

try:
    print("1. Testing ai_agents import...")
    import ai_agents
    print("✅ ai_agents imported")
    
    print("2. Testing agents module...")
    import ai_agents.agents
    print("✅ agents module imported")
    
    print("3. Testing agent_base...")
    from ai_agents.agents.agent_base import AgentBase
    print("✅ AgentBase imported")
    
    print("4. Testing langchain imports...")
    from langchain_core.tools import BaseTool
    from langchain.agents import AgentExecutor, create_openai_functions_agent
    from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
    print("✅ LangChain imports successful")
    
    print("5. Testing KPIT agent class definition...")
    # Just test the class definition without instantiation
    exec("""
class TestKPITAgent(AgentBase):
    def __init__(self, config):
        self.config = config
    
    def build_agent(self):
        return None
    
    def run(self, input_data):
        return {}
""")
    print("✅ Agent class definition works")
    
    print("\n✅ All basic imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
