#!/usr/bin/env python3
"""Test script for KPIT Agent."""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_agents.agents.registry import get_agent


def test_kpit_agent():
    """Test the KPIT agent with a simple query."""
    try:
        # Get the agent
        agent = get_agent("KPIT_agent")
        
        print("KPIT Agent initialized successfully!")
        print(f"Available tools: {[tool.name for tool in agent.tools]}")
        
        # Test with a simple query
        test_query = "List all available tools and their descriptions"
        
        print(f"\nTesting with query: {test_query}")
        
        result = agent.run({"query": test_query})
        
        print("\nResult:")
        print(f"Query: {result['query']}")
        print(f"Response: {result['response']}")
        print(f"Tools used: {result['metadata']['tools_used']}")
        
        return True
        
    except Exception as e:
        print(f"Error testing KPIT agent: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_kpit_agent()
    if success:
        print("\n✅ KPIT Agent test completed successfully!")
    else:
        print("\n❌ KPIT Agent test failed!")
        sys.exit(1)
