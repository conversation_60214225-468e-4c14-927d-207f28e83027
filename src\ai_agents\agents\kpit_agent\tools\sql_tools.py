"""SQL Database Tools for KPIT Agent."""

import json
from typing import Any, Dict
from langchain_core.tools import BaseTool
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit


class SQLDatabaseListTablesTool(BaseTool):
    """Tool for listing tables in the SQL database."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the SQL Database List Tables tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config and 'db' in self.config and self.config['db'] is not None:
            self.db = self.config['db']
            self.llm = self.config.get('llm')
            
            # Initialize SQL toolkit to get the list tables tool
            if self.llm:
                toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
                sql_tools = toolkit.get_tools()
                
                # Find the list tables tool
                self.list_tables_tool = None
                for tool in sql_tools:
                    if tool.name == "sql_db_list_tables":
                        self.list_tables_tool = tool
                        break
            else:
                self.list_tables_tool = None
        else:
            self.db = None
            self.list_tables_tool = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_list_tables"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return "List all tables in the SQL database. No input required."

    def _run(self, input_str: str = "") -> str:
        """Execute the tool."""
        try:
            if self.list_tables_tool is None:
                return "SQL database not configured or not available."
            
            result = self.list_tables_tool._run("")
            return result
            
        except Exception as e:
            return f"Error listing tables: {str(e)}"


class SQLDatabaseSchemaTool(BaseTool):
    """Tool for getting schema information of SQL database tables."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the SQL Database Schema tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config and 'db' in self.config and self.config['db'] is not None:
            self.db = self.config['db']
            self.llm = self.config.get('llm')
            
            # Initialize SQL toolkit to get the schema tool
            if self.llm:
                toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
                sql_tools = toolkit.get_tools()
                
                # Find the schema tool
                self.schema_tool = None
                for tool in sql_tools:
                    if tool.name == "sql_db_schema":
                        self.schema_tool = tool
                        break
            else:
                self.schema_tool = None
        else:
            self.db = None
            self.schema_tool = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_schema"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get schema information for SQL database tables. "
                "Input should be table names separated by commas.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.schema_tool is None:
                return "SQL database not configured or not available."
            
            result = self.schema_tool._run(input_str)
            return result
            
        except Exception as e:
            return f"Error getting schema: {str(e)}"


class SQLDatabaseQueryCheckerTool(BaseTool):
    """Tool for checking SQL queries before execution."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the SQL Database Query Checker tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config and 'db' in self.config and self.config['db'] is not None:
            self.db = self.config['db']
            self.llm = self.config.get('llm')
            
            # Initialize SQL toolkit to get the query checker tool
            if self.llm:
                toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
                sql_tools = toolkit.get_tools()
                
                # Find the query checker tool
                self.query_checker_tool = None
                for tool in sql_tools:
                    if tool.name == "sql_db_query_checker":
                        self.query_checker_tool = tool
                        break
            else:
                self.query_checker_tool = None
        else:
            self.db = None
            self.query_checker_tool = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_query_checker"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Check SQL query for correctness before execution. "
                "Input should be the SQL query string.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.query_checker_tool is None:
                return "SQL database not configured or not available."
            
            # Remove outer quotes but preserve inner quotes in SQL
            query = input_str.strip()
            if (query.startswith('"') and query.endswith('"')) or \
               (query.startswith("'") and query.endswith("'")):
                query = query[1:-1]
            
            result = self.query_checker_tool._run(query)
            return result
            
        except Exception as e:
            return f"Error checking query: {str(e)}"


class SQLDatabaseQueryTool(BaseTool):
    """Tool for executing SQL queries."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the SQL Database Query tool."""
        super().__init__()
        self.config = config or {}
        
        if self.config and 'db' in self.config and self.config['db'] is not None:
            self.db = self.config['db']
            self.llm = self.config.get('llm')
            
            # Initialize SQL toolkit to get the query tool
            if self.llm:
                toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
                sql_tools = toolkit.get_tools()
                
                # Find the query tool
                self.query_tool = None
                for tool in sql_tools:
                    if tool.name == "sql_db_query":
                        self.query_tool = tool
                        break
            else:
                self.query_tool = None
        else:
            self.db = None
            self.query_tool = None

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_query"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Execute SQL queries against the database. "
                "Input should be the SQL query string.")

    def _run(self, input_str: str) -> str:
        """Execute the tool."""
        try:
            if self.query_tool is None:
                return "SQL database not configured or not available."
            
            # Remove outer quotes but preserve inner quotes in SQL
            query = input_str.strip()
            if (query.startswith('"') and query.endswith('"')) or \
               (query.startswith("'") and query.endswith("'")):
                query = query[1:-1]
            
            if not query:
                return "Query cannot be empty"
            
            result = self.query_tool._run(query)
            return result
            
        except Exception as e:
            return f"Error executing query: {str(e)}"
