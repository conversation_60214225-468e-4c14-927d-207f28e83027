"""GitLab File SHA from Branch Timestamp Tool.

This tool retrieves the SHA hash of a file from the latest commit on a branch
before a specific timestamp.
"""

import json
from typing import Any, Dict
from datetime import datetime
from base_tool import BaseTool
from tools import make_gitlab_tools, GitLabAPIError


class GetFileSHAFromBranchTimestampTool(BaseTool):
    """Tool for getting SHA hash of a file from branch at specific timestamp."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the GitLab File SHA from Branch Timestamp tool.

        Args:
            config (dict): Configuration containing GitLab settings:
                - gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
                - project_id: GitLab project ID
                - token: GitLab access token
        """
        super().__init__(config)
        
        if not self.config:
            raise ValueError("GitLab configuration is required")
        
        required_keys = ['gitlab_url', 'project_id', 'token']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        # Initialize GitLab tools
        _, self.get_sha_ts_func, _ = make_gitlab_tools(self.config)

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_sha_from_branch_timestamp"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get SHA hash of a file from latest commit on branch before a specific timestamp. "
                "Input should contain 'branch', 'timestamp', and 'file_path' keys.")

    def _normalize_timestamp(self, timestamp: str) -> str:
        """
        Normalize timestamp to ISO format.
        
        Args:
            timestamp (str): Input timestamp in various formats
            
        Returns:
            str: Normalized timestamp in ISO format
        """
        try:
            # Try parsing different timestamp formats
            formats = [
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d",
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(timestamp, fmt)
                    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
                except ValueError:
                    continue
            
            # If no format matches, return as-is
            return timestamp
            
        except Exception:
            return timestamp

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_sha_from_branch_timestamp"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get SHA hash of a file from latest commit on branch before a specific timestamp. "
                "Input should contain 'branch', 'timestamp', and 'file_path' keys.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get file SHA hash from branch at timestamp.

        Args:
            input_data (dict): Input parameters containing:
                - branch (str): Branch name (default: 'main')
                - timestamp (str): ISO format timestamp
                - file_path (str): Path to the file in the repository

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - sha (str): SHA hash of the file (if successful)
                - error (str): Error message (if failed)
        """
        try:
            # Validate input
            if 'timestamp' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'timestamp'"
                }
            
            if 'file_path' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'file_path'"
                }
            
            branch = input_data.get('branch', 'main')
            timestamp = input_data['timestamp']
            file_path = input_data['file_path']
            
            # Normalize timestamp
            normalized_timestamp = self._normalize_timestamp(timestamp)
            
            # Get SHA hash using GitLab API
            sha = self.get_sha_ts_func(branch, normalized_timestamp, file_path)
            
            return {
                'success': True,
                'sha': sha,
                'branch': branch,
                'timestamp': normalized_timestamp,
                'file_path': file_path
            }
            
        except GitLabAPIError as e:
            return {
                'success': False,
                'error': f"GitLab API Error: {str(e)}"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a JSON string input (for LangChain compatibility).

        Args:
            input_str (str): JSON string with 'branch', 'timestamp', and 'file_path' keys
                Example: '{"branch": "main", "timestamp": "2024-01-15T10:30:00Z", "file_path": "config.py"}'

        Returns:
            str: SHA hash or error message
        """
        try:
            params = json.loads(input_str)
            result = self.run(params)
            
            if result['success']:
                return result['sha']
            else:
                return result['error']
                
        except json.JSONDecodeError as e:
            return f"Error parsing input: {str(e)}. Expected JSON with 'branch', 'timestamp', and 'file_path' keys."
        except Exception as e:
            return f"Error: {str(e)}"
